import { formatNumber } from '@/utils/format';
import * as HeadlessUI from '@headlessui/react';
import { 
    selectSelectedOrder, 
    selectSelectedVarieties, 
    selectVarietyExpectedPercentages, 
    updateVarietySelection,
    updateVarietyExpectedPercentage as updateVarietyPercentageAction, 
    selectWorkOrderForDate
} from './harvesting-work-orders-slice';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { HarvestingOrderVariety, HarvestingWorkOrder } from 'api/models/boekestyns';
import { classNames } from '@/utils/class-names';
import { useMemo } from 'react';

export type HarvestingWorkOrderVarietyRowProps = {
    variety: HarvestingOrderVariety;
    allWorkOrders: { [date: string]: Partial<HarvestingWorkOrder> };
    date: string;
    onUpdateWorkOrder: (date: string, updates: Partial<HarvestingWorkOrder>) => void;
};

export function HarvestingWorkOrderVarietyRow({ variety, allWorkOrders, date, onUpdateWorkOrder }: HarvestingWorkOrderVarietyRowProps) {
    const dispatch = useAppDispatch();
    const selectedVarieties = useAppSelector(selectSelectedVarieties);
    const workOrder = useAppSelector((state) => selectWorkOrderForDate(state, date));

    const order = useAppSelector(selectSelectedOrder);
    const finalRound = workOrder.finalRound || false;

    const expectedHarvestPercentage = useMemo(() => {
        const workOrderVariety = workOrder.varieties?.find(v => v.name === variety.name);

        // Check if variety exists in work order varieties and has a set percentage
        if (workOrderVariety && workOrderVariety.expectedHarvestPercentage !== null && workOrderVariety.expectedHarvestPercentage !== undefined) {
            return workOrderVariety.expectedHarvestPercentage;
        }

        // Only default to 100% for the last date if no percentage has been explicitly set
        const isLastDate = Object.keys(allWorkOrders).pop() === date;
        if (isLastDate && !workOrderVariety) {
            return 100;
        }

        // Return 0 as default for non-last dates or when no variety is found
        return 0;
    }, [variety, workOrder.varieties, allWorkOrders, date]);

    const varietyScheduledPots = useMemo(() => {
        const varietyData = order?.varieties.find(v => v.name === variety.name);
        if (!varietyData) return 0;

        return Object.keys(allWorkOrders).reduce((total, workOrderDate) => {
            if (workOrderDate >= date) return total;

            const workOrder = allWorkOrders[workOrderDate];
            if (!workOrder.varieties) return total;

            const varietyWorkOrder = workOrder.varieties.find(v => v.name === variety.name);
            if (!varietyWorkOrder || !varietyWorkOrder.expectedHarvestPercentage) return total;

            // Calculate scheduled pots based on expected percentage of currently available pots
            const availablePots = varietyData.pots - total;
            const scheduledPots = Math.round(availablePots * (varietyWorkOrder.expectedHarvestPercentage / 100));

            return total + scheduledPots;
        }, 0);
    }, [order?.varieties, allWorkOrders, variety, date]);

    const varietyAvailablePots = useMemo(() => {
        const varietyData = order?.varieties.find(v => v.name === variety.name);
        if (!varietyData) return 0;

        return varietyData.pots - varietyScheduledPots;
    }, [order?.varieties, variety.name, varietyScheduledPots]);



    const varietyToSchedule = useMemo(() => {
        if (finalRound) {
            return varietyAvailablePots;
        }

        const scheduledForThisRound = Math.round(varietyAvailablePots * (expectedHarvestPercentage / 100));

        return Math.min(scheduledForThisRound, varietyAvailablePots);
    }, [finalRound, varietyAvailablePots, expectedHarvestPercentage]);

    const handleSelectVarietyChanged = (checked: boolean, variety: string) => {
        dispatch(updateVarietySelection({ varietyName: variety, selected: checked }));

        if (checked && !workOrder.varieties?.find(v => v.name === variety)) {
            onUpdateWorkOrder(date, { varieties: [
            ...workOrder.varieties || [],
            {
                id: 0,
                workOrderId: 0,
                name: variety,
                pots: 0,
                cases: 0,
                beginningQuantity: varietyAvailablePots,
                expectedHarvestPercentage: expectedHarvestPercentage,
                comment: null
            }
            ]});
        } else if (!checked) {
            onUpdateWorkOrder(date, { varieties: workOrder.varieties?.filter(v => v.name !== variety) });
        }
    };

    const handleExpectedHarvestPercentageChange = (e: React.ChangeEvent<HTMLInputElement>, variety: string) => {
        const value = e.target.valueAsNumber;
        const percentage = isNaN(value) ? 0 : value;

        // Update the parent component's work order data
        const currentVarieties = workOrder.varieties || [];
        const updatedVarieties = currentVarieties.map(v =>
            v.name === variety ? { ...v, expectedHarvestPercentage: percentage } : v
        );

        onUpdateWorkOrder(date, { varieties: updatedVarieties });

        // Also update the slice state for consistency
        dispatch(updateVarietyPercentageAction({ date, varietyName: variety, percentage }));
    };

    return (
        <tr key={variety.name}>
            <td className="whitespace-nowrap p-2 text-left">
                {variety.name}
            </td>
            <td className="w-1 p-2 text-right">
                {formatNumber(variety.pots, '0,0')}
            </td>
            <td className="w-1 p-2 text-right">
                {formatNumber(varietyScheduledPots, '0,0')}
            </td>
            <td className="w-1 p-2 text-right">
                {formatNumber(varietyAvailablePots, '0,0')}
            </td>
            <td className="w-1 p-2 text-right">
                {formatNumber(varietyToSchedule, '0,0')}
            </td>
            <td className="w-1 p-2 text-right">
                <input
                type="number"
                value={expectedHarvestPercentage}
                onChange={(e) => handleExpectedHarvestPercentageChange(e, variety.name)}
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                min="0"
                max="100"
                step="1"
                />
            </td>
            <td className="text-center">
                <HeadlessUI.Switch
                onChange={(checked) =>
                    handleSelectVarietyChanged(checked, variety.name)
                }
                checked={selectedVarieties?.[variety.name] || false}
                className={classNames(
                    selectedVarieties?.[variety.name] ? 'bg-blue-400' : 'bg-gray-200',
                    'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
                )}
                >
                <span
                    aria-hidden="true"
                    className={classNames(
                    selectedVarieties?.[variety.name] ? 'translate-x-5' : 'translate-x-0',
                    'pointer-events-none inline-block size-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
                    )}
                />
                </HeadlessUI.Switch>
            </td>
        </tr>
    );
}