import { HarvestingOrder, HarvestingWorkOrder} from "api/models/boekestyns";
import moment from 'moment';
import { useState } from "react";
import { useAppDispatch } from '@/services/hooks';
import {
  setVarietyExpectedPercentages,
} from './harvesting-work-orders-slice';
import { HarvestingWorkOrderVarietyRow } from "./harvesting-work-order-variety";

export type HarvestingWorkOrderDayProps = {
  workOrder: Partial<HarvestingWorkOrder>;
  allWorkOrders: { [key: string]: Partial<HarvestingWorkOrder>; };
  order: HarvestingOrder | null;
  date: string;
  onUpdateWorkOrder: (date: string, updates: Partial<HarvestingWorkOrder>) => void;
};

export function HarvestingWorkOrderDay({ workOrder, allWorkOrders, order, date, onUpdateWorkOrder}: HarvestingWorkOrderDayProps) {
  const dispatch = useAppDispatch();

  // Use state from parent component instead of local state
  const crewSize = workOrder.crewSize || 1;
  const comments = workOrder.harvestingComments || null;

  const [defaultExpectedHarvestPercentage, setDefaultExpectedHarvestPercentage] = useState<number | null>(workOrder.defaultExpectedHarvestPercentage || null);

  // Helper functions to update work order data
  const updateCrewSize = (newCrewSize: number) => {
    onUpdateWorkOrder(date, { crewSize: newCrewSize });
  };

  const updateComments = (newComments: string) => {
    onUpdateWorkOrder(date, { harvestingComments: newComments });
  };

  // Helper function to calculate available pots for a variety on a specific date
  const calculateVarietyAvailablePots = (varietyName: string) => {
    const varietyData = order?.varieties.find(v => v.name === varietyName);
    if (!varietyData) return 0;

    // Calculate already scheduled pots from previous work orders
    const varietyScheduledPots = Object.keys(allWorkOrders).reduce((total, workOrderDate) => {
      if (workOrderDate >= date) return total;

      const workOrder = allWorkOrders[workOrderDate];
      if (!workOrder.varieties) return total;

      const varietyWorkOrder = workOrder.varieties.find(v => v.name === varietyName);
      if (!varietyWorkOrder || !varietyWorkOrder.expectedHarvestPercentage) return total;

      // Calculate scheduled pots based on expected percentage of currently available pots
      const availablePots = varietyData.pots - total;
      const scheduledPots = Math.round(availablePots * (varietyWorkOrder.expectedHarvestPercentage / 100));

      return total + scheduledPots;
    }, 0);

    return varietyData.pots - varietyScheduledPots;
  };

  const updateVarietyExpectedPercentage = (varietyNames: string[], percentage: number) => {
    const currentVarieties = workOrder.varieties || [];
    /*const updatedVarieties = currentVarieties.map(variety =>
      variety.name === varietyName
        ? { ...variety, expectedHarvestPercentage: percentage }
        : variety
    );*/
    const updatedVarieties = varietyNames.map(varietyName => {
      const variety = currentVarieties.find(v => v.name === varietyName);
      if (variety) {
        return { ...variety, expectedHarvestPercentage: percentage };
      } else {
        // Calculate the correct beginning quantity for this variety
        const availablePots = calculateVarietyAvailablePots(varietyName);

        return {
          id: 0, // Will be set by backend
          workOrderId: 0, // Will be set by backend
          name: varietyName,
          pots: 0,
          cases: 0,
          beginningQuantity: availablePots,
          expectedHarvestPercentage: percentage,
          comment: null
        };
      }
    });

    onUpdateWorkOrder(date, { varieties: updatedVarieties });
  };

  const handleApplyDefaultPercentage = () => {
    const varietyNames = order?.varieties.map(v => v.name) || [];
    updateVarietyExpectedPercentage(varietyNames, defaultExpectedHarvestPercentage || 0);
    dispatch(setVarietyExpectedPercentages(
      varietyNames.map(varietyName => ({ date, varietyName, percentage: defaultExpectedHarvestPercentage || 0 }))
    ));
    onUpdateWorkOrder(date, { defaultExpectedHarvestPercentage });
  };

  return (
    <div className="p-4">
      <h3 className="text-lg font-semibold mb-4">
        Work Order for {moment(date).format('dddd, MMMM D, YYYY')}
      </h3>

        <div className="rounded border p-8">
          <div className="flex justify-between">
            <h3 className="text-lg font-semibold text-gray-900">
              {order?.orderNumber}
              <div className="italic">
                {order?.plant.name}
              </div>
            </h3>
            <div className="flex items-center">
              <label htmlFor="defaultPercentage" className="mr-2 text-sm font-medium text-gray-700">
                Default Percentage:
              </label>
              <input
                type="number"
                min="0"
                max="100"
                defaultValue={workOrder.defaultExpectedHarvestPercentage || 0}
                onChange={(e) => setDefaultExpectedHarvestPercentage(e.target.valueAsNumber)}
                className="block w-24 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
              <button onClick={handleApplyDefaultPercentage} className="ml-2 btn-secondary ">
                Apply to all
              </button>
            </div>
          </div>
          
          <div>
            <table className="min-w-full divide-y divide-gray-300 text-sm">
              <thead>
                <tr>
                  <th className="p-2">Variety</th>
                  <th className="w-1 p-2 text-right">Total Planted</th>
                  <th className="w-1 p-2 text-right">Already Scheduled</th>
                  <th className="w-1 p-2 text-right">Available</th>
                  <th className="w-1 whitespace-nowrap p-2 text-right">
                    To Schedule
                  </th>
                  <th className="w-1 p-2 text-right">Expected Percentage</th>
                  <th>&nbsp;</th>
                </tr>
              </thead>
              <tbody>
                {order?.varieties.map((variety) => (
                  <HarvestingWorkOrderVarietyRow
                    key={variety.name}
                    variety={variety}
                    allWorkOrders={allWorkOrders}
                    date={date}
                    onUpdateWorkOrder={onUpdateWorkOrder}
                  />
                ))}
              </tbody>
            </table>
          </div>
          <div className="mt-4 grid grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-500">
                Crew Size
              </label>
              <input
                type="number"
                value={crewSize}
                onChange={(e) => updateCrewSize(e.target.valueAsNumber)}
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </div>
            <div className="col-span-3">
              <label className="block text-sm font-medium text-gray-500">
                Comments
              </label>
              <textarea
                rows={3}
                value={comments ?? ''}
                onChange={(e) => updateComments(e.target.value)}
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>
      </div>
  );
}
